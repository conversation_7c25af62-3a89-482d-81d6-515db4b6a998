{"_from": "camelcase@^1.0.2", "_id": "camelcase@1.2.1", "_inBundle": false, "_integrity": "sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==", "_location": "/camelcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "camelcase@^1.0.2", "name": "camelcase", "escapedName": "camelcase", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "_shasum": "9bb5304d2e0b56698b2c758b08a3eaa9daa58a39", "_spec": "camelcase@^1.0.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/yargs", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/camelcase/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert a dash/dot/underscore/space separated string to camelCase: foo-bar → fooBar", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/camelcase#readme", "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert"], "license": "MIT", "name": "camelcase", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/camelcase.git"}, "scripts": {"test": "node test.js"}, "version": "1.2.1"}