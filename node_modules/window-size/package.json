{"_from": "window-size@0.1.0", "_id": "window-size@0.1.0", "_inBundle": false, "_integrity": "sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg==", "_location": "/window-size", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "window-size@0.1.0", "name": "window-size", "escapedName": "window-size", "rawSpec": "0.1.0", "saveSpec": null, "fetchSpec": "0.1.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "_shasum": "5438cd2ea93b202efa3a19fe8887aee7c94f9c9d", "_spec": "window-size@0.1.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/yargs", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/window-size/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Reliable way to to get the height and width of the terminal/console in a node.js environment.", "engines": {"node": ">= 0.8.0"}, "homepage": "https://github.com/jonschlinkert/window-size", "keywords": ["window", "console", "terminal", "tty"], "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/window-size/blob/master/LICENSE-MIT"}], "main": "index.js", "name": "window-size", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/window-size.git"}, "version": "0.1.0"}