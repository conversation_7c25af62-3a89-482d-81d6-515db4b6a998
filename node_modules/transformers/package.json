{"_from": "transformers@2.1.0", "_id": "transformers@2.1.0", "_inBundle": false, "_integrity": "sha512-zJf5m2EIOngmBbDe2fhTPpCjzM2qkZVqrFJZc2jaln+KBeEaYKhS2QMOIkfVrNUyoOwqgbTwOHATzr3jZRQDyg==", "_location": "/transformers", "_phantomChildren": {"amdefine": "1.0.1", "optimist": "0.3.7"}, "_requested": {"type": "version", "registry": true, "raw": "transformers@2.1.0", "name": "transformers", "escapedName": "transformers", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/transformers/-/transformers-2.1.0.tgz", "_shasum": "5d23cb35561dd85dc67fb8482309b47d53cce9a7", "_spec": "transformers@2.1.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/ForbesLindesay/transformers/issues"}, "bundleDependencies": false, "dependencies": {"css": "~1.0.8", "promise": "~2.0", "uglify-js": "~2.2.5"}, "deprecated": "Deprecated, use jstransformer", "description": "String/Data transformations for use in templating libraries, static site generators and web frameworks", "devDependencies": {"atpl": "*", "coffee-script": "*", "coffeecup": "*", "coffeekup": "*", "component-builder": "*", "cson": "*", "dot": "*", "dust": "*", "dustjs-linkedin": "*", "eco": "*", "ect": "*", "ejs": "*", "expect.js": "~0.2", "haml-coffee": "*", "hamljs": "*", "handlebars": "*", "highlight.js": "*", "hogan.js": "*", "html2jade": "*", "jade": "*", "jazz": "*", "jqtpl": "*", "just": "*", "less": "*", "liquor": "*", "markdown": "*", "markdown-js": "*", "marked": "*", "mocha": "~1.8", "mote": "*", "mustache": "*", "plates": "*", "qejs": "*", "sass": "*", "stylus": "*", "supermarked": "*", "swig": "*", "templayed": "*", "then-jade": "*", "toffee": "*", "underscore": "*", "walrus": "*", "whiskers": "*"}, "gitHead": "4b46e72cba3ad3403fd5ed3802d5472dcfa77311", "homepage": "https://github.com/ForbesLindesay/transformers#readme", "license": "MIT", "main": "lib/transformers.js", "name": "transformers", "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/transformers.git"}, "scripts": {"pretest": "node test/update-package && npm install", "test": "mocha test/test.js -R spec"}, "version": "2.1.0"}