{"_from": "source-map@~0.1.7", "_id": "source-map@0.1.43", "_inBundle": false, "_integrity": "sha512-VtCvB9SIQhk3aF6h+N85EaqIaBFIAfZ9Cu+NJHHVvc8BbEcnvDcFw6sqQ2dQrT6SlOrZq3tIvyD9+EGq/lJryQ==", "_location": "/transformers/source-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-map@~0.1.7", "name": "source-map", "escapedName": "source-map", "rawSpec": "~0.1.7", "saveSpec": null, "fetchSpec": "~0.1.7"}, "_requiredBy": ["/transformers/uglify-js"], "_resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "_shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "_spec": "source-map@~0.1.7", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/transformers/node_modules/uglify-js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"amdefine": ">=0.0.4"}, "deprecated": false, "description": "Generates and consumes source maps", "devDependencies": {"dryice": ">=0.4.8"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.8.0"}, "homepage": "https://github.com/mozilla/source-map", "licenses": [{"type": "BSD", "url": "http://opensource.org/licenses/BSD-3-Clause"}], "main": "./lib/source-map.js", "name": "source-map", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"build": "node Makefile.dryice.js", "test": "node test/run-tests.js"}, "version": "0.1.43"}