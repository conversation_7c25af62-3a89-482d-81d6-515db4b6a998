{"_from": "promise@~2.0", "_id": "promise@2.0.0", "_inBundle": false, "_integrity": "sha512-OgMc+sxI3zWF8D5BJGtA0z7/IsrDy1/0cPaDv6HPpqa2fSTo7AdON5U10NbZCUeF+zCAj3PtfPE50Hf02386aA==", "_location": "/transformers/promise", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "promise@~2.0", "name": "promise", "escapedName": "promise", "rawSpec": "~2.0", "saveSpec": null, "fetchSpec": "~2.0"}, "_requiredBy": ["/transformers"], "_resolved": "https://registry.npmjs.org/promise/-/promise-2.0.0.tgz", "_shasum": "46648aa9d605af5d2e70c3024bf59436da02b80e", "_spec": "promise@~2.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/transformers", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/then/promise/issues"}, "bundleDependencies": false, "dependencies": {"is-promise": "~1"}, "deprecated": false, "description": "Bare bones Promises/A+ implementation", "devDependencies": {"better-assert": "~1.0.0", "mocha-as-promised": "~1.2.1", "promises-aplus-tests": "*"}, "homepage": "https://github.com/then/promise#readme", "license": "MIT", "main": "index.js", "name": "promise", "repository": {"type": "git", "url": "git+https://github.com/then/promise.git"}, "scripts": {"test": "mocha -R spec --timeout 200 --slow 99999"}, "version": "2.0.0"}