{"_from": "is-promise@~1", "_id": "is-promise@1.0.1", "_inBundle": false, "_integrity": "sha512-mjWH5XxnhMA8cFnDchr6qRP9S/kLntKuEfIYku+PaN1CnS8v+OG9O/BKpRCVRJvpIkgAZm0Pf5Is3iSSOILlcg==", "_location": "/transformers/is-promise", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-promise@~1", "name": "is-promise", "escapedName": "is-promise", "rawSpec": "~1", "saveSpec": null, "fetchSpec": "~1"}, "_requiredBy": ["/transformers/promise"], "_resolved": "https://registry.npmjs.org/is-promise/-/is-promise-1.0.1.tgz", "_shasum": "31573761c057e33c2e91aab9e96da08cefbe76e5", "_spec": "is-promise@~1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/transformers/node_modules/promise", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/then/is-promise/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Test whether an object looks like a promises-a+ promise", "devDependencies": {"better-assert": "~0.1.0", "mocha": "~1.7.4"}, "homepage": "https://github.com/then/is-promise#readme", "license": "MIT", "main": "index.js", "name": "is-promise", "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "scripts": {"test": "mocha -R spec"}, "version": "1.0.1"}