{"_from": "uglify-js@~2.2.5", "_id": "uglify-js@2.2.5", "_inBundle": false, "_integrity": "sha512-viLk+/8G0zm2aKt1JJAVcz5J/5ytdiNaIsKgrre3yvSUjwVG6ZUujGH7E2TiPigZUwLYCe7eaIUEP2Zka2VJPA==", "_location": "/transformers/uglify-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uglify-js@~2.2.5", "name": "uglify-js", "escapedName": "uglify-js", "rawSpec": "~2.2.5", "saveSpec": null, "fetchSpec": "~2.2.5"}, "_requiredBy": ["/transformers"], "_resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.2.5.tgz", "_shasum": "a6e02a70d839792b9780488b7b8b184c095c99c7", "_spec": "uglify-js@~2.2.5", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/transformers", "bin": {"uglifyjs": "bin/uglifyjs"}, "bugs": {"url": "https://github.com/mishoo/UglifyJS2/issues"}, "bundleDependencies": false, "dependencies": {"optimist": "~0.3.5", "source-map": "~0.1.7"}, "deprecated": false, "description": "JavaScript parser, mangler/compressor and beautifier toolkit", "engines": {"node": ">=0.4.0"}, "homepage": "http://lisperator.net/uglifyjs", "main": "tools/node.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}], "name": "uglify-js", "repositories": [{"type": "git", "url": "git+https://github.com/mishoo/UglifyJS2.git"}], "repository": {"type": "git", "url": "git+https://github.com/mishoo/UglifyJS2.git"}, "scripts": {"test": "node test/run-tests.js"}, "version": "2.2.5"}