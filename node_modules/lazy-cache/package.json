{"_from": "lazy-cache@^1.0.3", "_id": "lazy-cache@1.0.4", "_inBundle": false, "_integrity": "sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ==", "_location": "/lazy-cache", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lazy-cache@^1.0.3", "name": "lazy-cache", "escapedName": "lazy-cache", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/center-align"], "_resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "_shasum": "a1d78fc3a50474cb80845d3b3b6e1da49a446e8e", "_spec": "lazy-cache@^1.0.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/center-align", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/lazy-cache/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Cache requires to be lazy-loaded when needed.", "devDependencies": {"ansi-yellow": "^0.1.1", "glob": "^7.0.3", "gulp-format-md": "^0.1.8", "mocha": "^2.4.5"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/lazy-cache", "keywords": ["cache", "caching", "dependencies", "dependency", "lazy", "require", "requires"], "license": "MIT", "main": "index.js", "name": "lazy-cache", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/lazy-cache.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["lint-deps"]}, "plugins": ["gulp-format-md"], "toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "version": "1.0.4"}