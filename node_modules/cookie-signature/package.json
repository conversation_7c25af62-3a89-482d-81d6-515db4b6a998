{"_from": "cookie-signature@1.0.6", "_id": "cookie-signature@1.0.6", "_inBundle": false, "_integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==", "_location": "/cookie-signature", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookie-signature@1.0.6", "name": "cookie-signature", "escapedName": "cookie-signature", "rawSpec": "1.0.6", "saveSpec": null, "fetchSpec": "1.0.6"}, "_requiredBy": ["/cookie-parser", "/express"], "_resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "_shasum": "e303a882b342cc3ee8ca513a79999734dab3ae2c", "_spec": "cookie-signature@1.0.6", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/cookie-parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Sign and unsign cookies", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "keywords": ["cookie", "sign", "unsign"], "license": "MIT", "main": "index", "name": "cookie-signature", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-cookie-signature.git"}, "scripts": {"test": "mocha --require should --reporter spec"}, "version": "1.0.6"}