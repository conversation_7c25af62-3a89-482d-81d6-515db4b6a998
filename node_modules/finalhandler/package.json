{"_from": "finalhandler@1.1.1", "_id": "finalhandler@1.1.1", "_inBundle": false, "_integrity": "sha512-Y1GUDo39ez4aHAw7MysnUD5JzYX+WaIj8I57kO3aEPT1fFRL4sr7mjei97FgnwhAyyzRYmQZaTHb2+9uZ1dPtg==", "_location": "/finalhandler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "finalhandler@1.1.1", "name": "finalhandler", "escapedName": "finalhandler", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.1.tgz", "_shasum": "eebf4ed840079c83f4249038c9d703008301b105", "_spec": "finalhandler@1.1.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "bundleDependencies": false, "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "statuses": "~1.4.0", "unpipe": "~1.0.0"}, "deprecated": false, "description": "Node.js final http responder", "devDependencies": {"eslint": "4.18.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.9.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3", "readable-stream": "2.3.4", "safe-buffer": "5.1.1", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/pillarjs/finalhandler#readme", "license": "MIT", "name": "finalhandler", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/finalhandler.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.1.1"}