{"_from": "css@~1.0.8", "_id": "css@1.0.8", "_inBundle": false, "_integrity": "sha512-qmTYWhHk910nQWnGqMAiWWPQlB6tESiWgNebQJmiozOAGcBAQ1+U/UzUOkhdrcshlkSRRiKWodwmVvO0OmnIGg==", "_location": "/css", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "css@~1.0.8", "name": "css", "escapedName": "css", "rawSpec": "~1.0.8", "saveSpec": null, "fetchSpec": "~1.0.8"}, "_requiredBy": ["/transformers"], "_resolved": "https://registry.npmjs.org/css/-/css-1.0.8.tgz", "_shasum": "9386811ca82bccc9ee7fb5a732b1e2a317c8a3e7", "_spec": "css@~1.0.8", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/transformers", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bundleDependencies": false, "dependencies": {"css-parse": "1.0.4", "css-stringify": "1.0.5"}, "deprecated": false, "description": "CSS parser / stringifier using css-parse and css-stringify", "keywords": ["css", "parser", "stylesheet"], "main": "index", "name": "css", "version": "1.0.8"}