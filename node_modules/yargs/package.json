{"_from": "yargs@~3.10.0", "_id": "yargs@3.10.0", "_inBundle": false, "_integrity": "sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A==", "_location": "/yargs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yargs@~3.10.0", "name": "yargs", "escapedName": "yargs", "rawSpec": "~3.10.0", "saveSpec": null, "fetchSpec": "~3.10.0"}, "_requiredBy": ["/uglify-js"], "_resolved": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "_shasum": "f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1", "_spec": "yargs@~3.10.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/uglify-js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://CodeTunnel.com"}, "bugs": {"url": "https://github.com/bcoe/yargs/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/bcoe"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://chrisneedham.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nylen"}, {"name": "<PERSON>", "url": "https://github.com/fizker"}, {"name": "<PERSON>", "url": "https://github.com/linclark"}], "dependencies": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}, "deprecated": false, "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "devDependencies": {"chai": "^2.2.0", "coveralls": "^2.11.2", "hashish": "0.0.4", "mocha": "^2.2.1", "nyc": "^2.2.1", "standard": "^3.11.1"}, "engine": {"node": ">=0.4"}, "files": ["index.js", "lib", "completion.sh.hbs", "LICENSE"], "homepage": "https://github.com/bcoe/yargs#readme", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT", "main": "./index.js", "name": "yargs", "repository": {"type": "git", "url": "git+ssh://**************/bcoe/yargs.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "test": "standard && nyc mocha --check-leaks && nyc report"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "version": "3.10.0"}