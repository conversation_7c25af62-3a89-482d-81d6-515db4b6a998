{"_from": "asap@~1.0.0", "_id": "asap@1.0.0", "_inBundle": false, "_integrity": "sha512-Ej9qjcXY+8Tuy1cNqiwNMwFRXOy9UwgTeMA8LxreodygIPV48lx8PU1ecFxb5ZeU1DpMKxiq6vGLTxcitWZPbA==", "_location": "/asap", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "asap@~1.0.0", "name": "asap", "escapedName": "asap", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/promise"], "_resolved": "https://registry.npmjs.org/asap/-/asap-1.0.0.tgz", "_shasum": "b2a45da5fdfa20b0496fc3768cc27c12fa916a7d", "_spec": "asap@~1.0.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/promise", "bundleDependencies": false, "deprecated": false, "description": "High-priority task queue for Node.js and browsers", "keywords": ["event", "task", "queue"], "licenses": [{"type": "MIT", "url": "https://github.com/kriskowal/asap/raw/master/LICENSE.md"}], "main": "asap", "name": "asap", "version": "1.0.0"}