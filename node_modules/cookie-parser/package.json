{"_from": "cookie-parser@~1.4.4", "_id": "cookie-parser@1.4.7", "_inBundle": false, "_integrity": "sha512-nGUvgXnotP3BsjiLX2ypbQnWoGUPIIfHQNZkkC668ntrzGWEZVW70HDEB1qnNGMicPje6EttlIgzo51YSwNQGw==", "_location": "/cookie-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cookie-parser@~1.4.4", "name": "cookie-parser", "escapedName": "cookie-parser", "rawSpec": "~1.4.4", "saveSpec": null, "fetchSpec": "~1.4.4"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/cookie-parser/-/cookie-parser-1.4.7.tgz", "_shasum": "e2125635dfd766888ffe90d60c286404fa0e7b26", "_spec": "cookie-parser@~1.4.4", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/expressjs/cookie-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.6"}, "deprecated": false, "description": "Parse HTTP request cookies", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.2", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0", "supertest": "6.1.6"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/cookie-parser#readme", "keywords": ["cookie", "middleware"], "license": "MIT", "name": "cookie-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/cookie-parser.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.4.7"}