{"_from": "wordwrap@0.0.2", "_id": "wordwrap@0.0.2", "_inBundle": false, "_integrity": "sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==", "_location": "/cliui/wordwrap", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "wordwrap@0.0.2", "name": "wordwrap", "escapedName": "wordwrap", "rawSpec": "0.0.2", "saveSpec": null, "fetchSpec": "0.0.2"}, "_requiredBy": ["/cliui"], "_resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "_shasum": "b79669bb42ecb409f83d583cad52ca17eaa1643f", "_spec": "wordwrap@0.0.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/cliui", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-wordwrap/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Wrap those words. Show them at what columns to start and stop.", "devDependencies": {"expresso": "=0.7.x"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/substack/node-wordwrap#readme", "keywords": ["word", "wrap", "rule", "format", "column"], "license": "MIT/X11", "main": "./index.js", "name": "wordwrap", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "scripts": {"test": "expresso"}, "version": "0.0.2"}