{"_from": "cliui@^2.1.0", "_id": "cliui@2.1.0", "_inBundle": false, "_integrity": "sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==", "_location": "/cliui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cliui@^2.1.0", "name": "cliui", "escapedName": "cliui", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "_shasum": "4b475760ff80264c762c3a1719032e91c7fea0d1", "_spec": "cliui@^2.1.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/bcoe/cliui/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": ["index.js"], "data-cover-never": ["node_modules", "test"], "output-reporter": "spec"}}, "dependencies": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}, "deprecated": false, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"blanket": "^1.1.6", "chai": "^2.2.0", "coveralls": "^2.11.2", "mocha": "^2.2.4", "mocha-lcov-reporter": "0.0.2", "mocoverage": "^1.0.0", "patched-blanket": "^1.0.1", "standard": "^3.6.1"}, "homepage": "https://github.com/bcoe/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "index.js", "name": "cliui", "repository": {"type": "git", "url": "git+ssh://**************/bcoe/cliui.git"}, "scripts": {"test": "standard && mocha --check-leaks --ui exports --require patched-blanket -R mocoverage"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "version": "2.1.0"}