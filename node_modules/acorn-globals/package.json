{"_from": "acorn-globals@^1.0.3", "_id": "acorn-globals@1.0.9", "_inBundle": false, "_integrity": "sha512-j3/4pkfih8W4NK22gxVSXcEonTpAHOHh0hu5BoZrKcOsW/4oBPxTi4Yk3SAj+FhC1f3+bRTkXdm4019gw1vg9g==", "_location": "/acorn-globals", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "acorn-globals@^1.0.3", "name": "acorn-globals", "escapedName": "acorn-globals", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/with"], "_resolved": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-1.0.9.tgz", "_shasum": "55bb5e98691507b74579d0513413217c380c54cf", "_spec": "acorn-globals@^1.0.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/with", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/ForbesLindesay/acorn-globals/issues"}, "bundleDependencies": false, "dependencies": {"acorn": "^2.1.0"}, "deprecated": false, "description": "Detect global variables in JavaScript using acorn", "devDependencies": {"testit": "^2.0.2"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/ForbesLindesay/acorn-globals#readme", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "license": "MIT", "name": "acorn-globals", "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/acorn-globals.git"}, "scripts": {"test": "node test"}, "version": "1.0.9"}