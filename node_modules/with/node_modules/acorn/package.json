{"_from": "acorn@^1.0.1", "_id": "acorn@1.2.2", "_inBundle": false, "_integrity": "sha512-FsqWmApWGMGLKKNpHt12PMc5AK7BaZee0WRh04fCysmTzHe+rrKOa2MKjORhnzfpe4r0JnfdqHn02iDA9Dqj2A==", "_location": "/with/acorn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "acorn@^1.0.1", "name": "acorn", "escapedName": "acorn", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/with"], "_resolved": "https://registry.npmjs.org/acorn/-/acorn-1.2.2.tgz", "_shasum": "c8ce27de0acc76d896d2b1fad3df588d9e82f014", "_spec": "acorn@^1.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/with", "bin": {"acorn": "bin/acorn"}, "bugs": {"url": "https://github.com/marijnh/acorn/issues"}, "bundleDependencies": false, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "deprecated": false, "description": "ECMAScript parser", "devDependencies": {"babelify": "^5.0.4", "browserify": "^9.0.3", "browserify-derequire": "^0.9.4", "unicode-7.0.0": "~0.1.5"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/marijnh/acorn", "license": "MIT", "main": "dist/acorn.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://rreverser.com/"}], "name": "acorn", "repository": {"type": "git", "url": "git+https://github.com/marijnh/acorn.git"}, "scripts": {"prepublish": "bin/prepublish.sh", "test": "node test/run.js"}, "version": "1.2.2"}