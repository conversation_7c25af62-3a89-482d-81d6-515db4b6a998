{"_from": "with@~4.0.0", "_id": "with@4.0.3", "_inBundle": false, "_integrity": "sha512-mJZFpyEc1JTAdxhi/vhVeAM2S7vsltEKDiexDDo1HuAzlYKhcVUU6cwY8cHrFYdt82ZNkfKCeyhA3IYFegI0Kg==", "_location": "/with", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "with@~4.0.0", "name": "with", "escapedName": "with", "rawSpec": "~4.0.0", "saveSpec": null, "fetchSpec": "~4.0.0"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/with/-/with-4.0.3.tgz", "_shasum": "eefd154e9e79d2c8d3417b647a8f14d9fecce14e", "_spec": "with@~4.0.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/ForbesLindesay/with/issues"}, "bundleDependencies": false, "dependencies": {"acorn": "^1.0.1", "acorn-globals": "^1.0.3"}, "deprecated": false, "description": "Compile time `with` for strict mode JavaScript", "devDependencies": {"mocha": "~1.12.0", "uglify-js": "^2.4.15"}, "homepage": "https://github.com/ForbesLindesay/with#readme", "license": "MIT", "main": "index.js", "name": "with", "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/with.git"}, "scripts": {"test": "mocha test/index.js -R spec"}, "version": "4.0.3"}