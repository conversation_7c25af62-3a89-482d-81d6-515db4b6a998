{"_from": "on-headers@~1.0.1", "_id": "on-headers@1.0.2", "_inBundle": false, "_integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==", "_location": "/on-headers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "on-headers@~1.0.1", "name": "on-headers", "escapedName": "on-headers", "rawSpec": "~1.0.1", "saveSpec": null, "fetchSpec": "~1.0.1"}, "_requiredBy": ["/morgan"], "_resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "_shasum": "772b0ae6aaa525c399e489adfad90c403eb3c28f", "_spec": "on-headers@~1.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/morgan", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Execute a listener when a response is about to write headers", "devDependencies": {"eslint": "5.14.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "6.0.1", "supertest": "3.4.2"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/on-headers#readme", "keywords": ["event", "headers", "http", "onheaders"], "license": "MIT", "name": "on-headers", "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-headers.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.0.2"}