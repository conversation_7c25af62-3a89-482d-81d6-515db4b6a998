{"_from": "merge-descriptors@1.0.1", "_id": "merge-descriptors@1.0.1", "_inBundle": false, "_integrity": "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==", "_location": "/merge-descriptors", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "merge-descriptors@1.0.1", "name": "merge-descriptors", "escapedName": "merge-descriptors", "rawSpec": "1.0.1", "saveSpec": null, "fetchSpec": "1.0.1"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "_shasum": "b00aaa556dd8b44568150ec9d1b953f3f90cbb61", "_spec": "merge-descriptors@1.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/component/merge-descriptors/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Merge objects using descriptors", "devDependencies": {"istanbul": "0.4.1", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/component/merge-descriptors#readme", "license": "MIT", "name": "merge-descriptors", "repository": {"type": "git", "url": "git+https://github.com/component/merge-descriptors.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.0.1"}