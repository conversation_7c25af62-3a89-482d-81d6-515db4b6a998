{"_from": "send@0.16.2", "_id": "send@0.16.2", "_inBundle": false, "_integrity": "sha512-E64YFPUssFHEFBvpbbjr44NCLtI1AohxQ8ZSiJjQLskAdKuriYEP6VyGEsRDH8ScozGpkaX1BGvhanqCwkcEZw==", "_location": "/send", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "send@0.16.2", "name": "send", "escapedName": "send", "rawSpec": "0.16.2", "saveSpec": null, "fetchSpec": "0.16.2"}, "_requiredBy": ["/express", "/serve-static"], "_resolved": "https://registry.npmjs.org/send/-/send-0.16.2.tgz", "_shasum": "6ecca1e0f8c156d141597559848df64730a6bbc1", "_spec": "send@0.16.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/send/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.6.2", "mime": "1.4.1", "ms": "2.0.0", "on-finished": "~2.3.0", "range-parser": "~1.2.0", "statuses": "~1.4.0"}, "deprecated": false, "description": "Better streaming static file server with Range and conditional-GET support", "devDependencies": {"after": "0.8.2", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/send#readme", "keywords": ["static", "file", "server"], "license": "MIT", "name": "send", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/send.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot"}, "version": "0.16.2"}