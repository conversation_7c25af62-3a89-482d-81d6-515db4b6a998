{"_from": "on-finished@~2.3.0", "_id": "on-finished@2.3.0", "_inBundle": false, "_integrity": "sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==", "_location": "/on-finished", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "on-finished@~2.3.0", "name": "on-finished", "escapedName": "on-finished", "rawSpec": "~2.3.0", "saveSpec": null, "fetchSpec": "~2.3.0"}, "_requiredBy": ["/body-parser", "/express", "/finalhandler", "/morgan", "/send"], "_resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "_shasum": "20f1336481b083cd75337992a16971aa2d906947", "_spec": "on-finished@~2.3.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"ee-first": "1.1.1"}, "deprecated": false, "description": "Execute a callback when a request closes, finishes, or errors", "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/on-finished#readme", "license": "MIT", "name": "on-finished", "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-finished.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "2.3.0"}