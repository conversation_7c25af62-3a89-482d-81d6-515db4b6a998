{"_from": "repeat-string@^1.5.2", "_id": "repeat-string@1.6.1", "_inBundle": false, "_integrity": "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==", "_location": "/repeat-string", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "repeat-string@^1.5.2", "name": "repeat-string", "escapedName": "repeat-string", "rawSpec": "^1.5.2", "saveSpec": null, "fetchSpec": "^1.5.2"}, "_requiredBy": ["/align-text"], "_resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "_shasum": "8dcae470e1c88abc2d600fff4a776286da75e637", "_spec": "repeat-string@^1.5.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/align-text", "author": {"name": "<PERSON>", "url": "http://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/repeat-string/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/doowb"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://linus.unnebäck.se"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tbusser.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "wooorm.com"}], "deprecated": false, "description": "Repeat the given string n times. Fastest implementation for repeating a string.", "devDependencies": {"ansi-cyan": "^0.1.1", "benchmarked": "^0.2.5", "gulp-format-md": "^0.1.11", "isobject": "^2.1.0", "mocha": "^3.1.2", "repeating": "^3.0.0", "text-table": "^0.2.0", "yargs-parser": "^4.0.2"}, "engines": {"node": ">=0.10"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/repeat-string", "keywords": ["fast", "fastest", "fill", "left", "left-pad", "multiple", "pad", "padding", "repeat", "repeating", "repetition", "right", "right-pad", "string", "times"], "license": "MIT", "main": "index.js", "name": "repeat-string", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/repeat-string.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["repeat-element"]}, "helpers": ["./benchmark/helper.js"], "reflinks": ["verb"]}, "version": "1.6.1"}