{"_from": "morgan@~1.9.1", "_id": "morgan@1.9.1", "_inBundle": false, "_integrity": "sha512-HQStPIV4y3afTiCYVxirakhlCfGkI161c76kKFca7Fk1JusM//Qeo1ej2XaMniiNeaZklMVrh3vTtIzpzwbpmA==", "_location": "/morgan", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "morgan@~1.9.1", "name": "morgan", "escapedName": "morgan", "rawSpec": "~1.9.1", "saveSpec": null, "fetchSpec": "~1.9.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/morgan/-/morgan-1.9.1.tgz", "_shasum": "0a8d16734a1d9afbc824b99df87e738e58e2da59", "_spec": "morgan@~1.9.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"basic-auth": "~2.0.0", "debug": "2.6.9", "depd": "~1.1.2", "on-finished": "~2.3.0", "on-headers": "~1.0.1"}, "deprecated": false, "description": "HTTP request logger middleware for node.js", "devDependencies": {"eslint": "5.5.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "2.5.3", "split": "1.0.1", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/expressjs/morgan#readme", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "name": "morgan", "repository": {"type": "git", "url": "git+https://github.com/expressjs/morgan.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "version": "1.9.1"}