{"_from": "constantinople@~3.0.1", "_id": "constantinople@3.0.2", "_inBundle": false, "_integrity": "sha512-UnEggAQrmhxuTxlb7n1OsTtagNXWUv2CRlOogZhWOU4jLK4EJEbF8UDSNxuGu+jVtWNtO2j51ab2H1wlBIzF/w==", "_location": "/constantinople", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "constantinople@~3.0.1", "name": "constantinople", "escapedName": "constantinople", "rawSpec": "~3.0.1", "saveSpec": null, "fetchSpec": "~3.0.1"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/constantinople/-/constantinople-3.0.2.tgz", "_shasum": "4b945d9937907bcd98ee575122c3817516544141", "_spec": "constantinople@~3.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/ForbesLindesay/constantinople/issues"}, "bundleDependencies": false, "dependencies": {"acorn": "^2.1.0"}, "deprecated": "Please update to at least constantinople 3.1.1", "description": "Determine whether a JavaScript expression evaluates to a constant (using UglifyJS)", "devDependencies": {"mocha": "*"}, "homepage": "https://github.com/ForbesLindesay/constantinople#readme", "keywords": [], "license": "MIT", "name": "constantinople", "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/constantinople.git"}, "scripts": {"test": "mocha -R spec"}, "version": "3.0.2"}