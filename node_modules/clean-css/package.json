{"_from": "clean-css@^3.1.9", "_id": "clean-css@3.4.28", "_inBundle": false, "_integrity": "sha512-aTWyttSdI2mYi07kWqHi24NUU9YlELFKGOAgFzZjDN1064DMAOy2FBuoyGmkKRlXkbpXd0EVHmiVkbKhKoirTw==", "_location": "/clean-css", "_phantomChildren": {"graceful-readlink": "1.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "clean-css@^3.1.9", "name": "clean-css", "escapedName": "clean-css", "rawSpec": "^3.1.9", "saveSpec": null, "fetchSpec": "^3.1.9"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/clean-css/-/clean-css-3.4.28.tgz", "_shasum": "bf1945e82fc808f55695e6ddeaec01400efd03ff", "_spec": "clean-css@^3.1.9", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jakub<PERSON><PERSON>owicz"}, "bin": {"cleancss": "bin/cleancss"}, "bugs": {"url": "https://github.com/jakubpawlowicz/clean-css/issues"}, "bundleDependencies": false, "dependencies": {"commander": "2.8.x", "source-map": "0.4.x"}, "deprecated": false, "description": "A well-tested CSS minifier", "devDependencies": {"browserify": "11.x", "http-proxy": "1.x", "jshint": "2.8.x", "nock": "2.x", "server-destroy": "1.x", "uglify-js": "2.4.x", "vows": "0.8.x"}, "engines": {"node": ">=0.10.0"}, "files": ["bin", "lib", "History.md", "index.js", "LICENSE"], "homepage": "https://github.com/jakubpawlowicz/clean-css", "keywords": ["css", "minifier"], "license": "MIT", "main": "index.js", "name": "clean-css", "repository": {"type": "git", "url": "git+https://github.com/jakubpawlowicz/clean-css.git"}, "scripts": {"bench": "node ./test/bench.js", "browserify": "browserify --standalone CleanCSS index.js | uglifyjs --compress --mangle -o cleancss-browser.js", "check": "jshint ./bin/cleancss .", "prepublish": "npm run check", "test": "vows"}, "version": "3.4.28"}