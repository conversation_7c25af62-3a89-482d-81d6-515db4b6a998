{"_from": "commander@2.8.x", "_id": "commander@2.8.1", "_inBundle": false, "_integrity": "sha512-+pJLBFVk+9ZZdlAOB5WuIElVPPth47hILFkmGym57aq8kwxsowvByvB0DHs1vQAhyMZzdcpTtF0VDKGkSDR4ZQ==", "_location": "/clean-css/commander", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "commander@2.8.x", "name": "commander", "escapedName": "commander", "rawSpec": "2.8.x", "saveSpec": null, "fetchSpec": "2.8.x"}, "_requiredBy": ["/clean-css"], "_resolved": "https://registry.npmjs.org/commander/-/commander-2.8.1.tgz", "_shasum": "06be367febfda0c330aa1e2a072d3dc9762425d4", "_spec": "commander@2.8.x", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/clean-css", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "bundleDependencies": false, "dependencies": {"graceful-readlink": ">= 1.0.0"}, "deprecated": false, "description": "the complete solution for node.js command-line programs", "devDependencies": {"should": ">= 0.0.1", "sinon": ">= 1.14.1"}, "engines": {"node": ">= 0.6.x"}, "files": ["index.js"], "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["command", "option", "parser"], "license": "MIT", "main": "index", "name": "commander", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"test": "make test"}, "version": "2.8.1"}