{"_from": "destroy@~1.0.4", "_id": "destroy@1.0.4", "_inBundle": false, "_integrity": "sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg==", "_location": "/destroy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "destroy@~1.0.4", "name": "destroy", "escapedName": "destroy", "rawSpec": "~1.0.4", "saveSpec": null, "fetchSpec": "~1.0.4"}, "_requiredBy": ["/send"], "_resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "_shasum": "978857442c44749e4206613e37946205826abd80", "_spec": "destroy@~1.0.4", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/send", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "destroy a stream if possible", "devDependencies": {"istanbul": "0.4.2", "mocha": "2.3.4"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/stream-utils/destroy#readme", "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "license": "MIT", "name": "destroy", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/destroy.git"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "version": "1.0.4"}