{"_from": "proxy-addr@~2.0.4", "_id": "proxy-addr@2.0.7", "_inBundle": false, "_integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "_location": "/proxy-addr", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "proxy-addr@~2.0.4", "name": "proxy-addr", "escapedName": "proxy-addr", "rawSpec": "~2.0.4", "saveSpec": null, "fetchSpec": "~2.0.4"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "_shasum": "f19fe69ceab311eeb94b42e70e8c2070f9ba1025", "_spec": "proxy-addr@~2.0.4", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "bundleDependencies": false, "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "deprecated": false, "description": "Determine address of proxied request", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "deep-equal": "1.0.1", "eslint": "7.26.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.4.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.10"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/proxy-addr#readme", "keywords": ["ip", "proxy", "x-forwarded-for"], "license": "MIT", "name": "proxy-addr", "repository": {"type": "git", "url": "git+https://github.com/jshttp/proxy-addr.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.0.7"}