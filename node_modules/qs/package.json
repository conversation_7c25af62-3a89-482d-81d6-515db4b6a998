{"_from": "qs@6.5.2", "_id": "qs@6.5.2", "_inBundle": false, "_integrity": "sha512-N5ZAX4/LxJmF+7wN74pUD6qAh9/wnvdQcjq9TZjevvXzSUo7bfmw91saqMjzGS2xq91/odN2dW/WOl7qQHNDGA==", "_location": "/qs", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "qs@6.5.2", "name": "qs", "escapedName": "qs", "rawSpec": "6.5.2", "saveSpec": null, "fetchSpec": "6.5.2"}, "_requiredBy": ["/body-parser", "/express"], "_resolved": "https://registry.npmjs.org/qs/-/qs-6.5.2.tgz", "_shasum": "cb3ae806e8740444584ef154ce8ee98d403f3e36", "_spec": "qs@6.5.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "bugs": {"url": "https://github.com/ljharb/qs/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {}, "deprecated": false, "description": "A querystring parser that supports nesting and arrays, with a depth limit", "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "browserify": "^16.2.0", "covert": "^1.1.0", "editorconfig-tools": "^0.1.1", "eslint": "^4.19.1", "evalmd": "^0.0.17", "iconv-lite": "^0.4.21", "mkdirp": "^0.5.1", "qs-iconv": "^1.0.4", "safe-publish-latest": "^1.1.1", "safer-buffer": "^2.1.2", "tape": "^4.9.0"}, "engines": {"node": ">=0.6"}, "homepage": "https://github.com/ljharb/qs", "keywords": ["querystring", "qs"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "qs", "repository": {"type": "git", "url": "git+https://github.com/ljharb/qs.git"}, "scripts": {"coverage": "covert test", "dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js", "lint": "eslint lib/*.js test/*.js", "prelint": "editorconfig-tools check * lib/* test/*", "prepublish": "safe-publish-latest && npm run dist", "pretest": "npm run --silent readme && npm run --silent lint", "readme": "evalmd README.md", "test": "npm run --silent coverage", "tests-only": "node test"}, "version": "6.5.2"}