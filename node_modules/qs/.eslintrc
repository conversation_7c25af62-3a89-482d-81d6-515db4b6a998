{
    "root": true,

    "extends": "@ljharb",

    "rules": {
        "complexity": 0,
        "consistent-return": 1,
		"func-name-matching": 0,
        "id-length": [2, { "min": 1, "max": 25, "properties": "never" }],
        "indent": [2, 4],
        "max-params": [2, 12],
        "max-statements": [2, 45],
        "no-continue": 1,
        "no-magic-numbers": 0,
        "no-restricted-syntax": [2, "BreakStatement", "DebuggerStatement", "ForInStatement", "LabeledStatement", "WithStatement"],
        "operator-linebreak": [2, "before"],
    }
}
