{"_from": "http-errors@~1.6.3", "_id": "http-errors@1.6.3", "_inBundle": false, "_integrity": "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==", "_location": "/http-errors", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-errors@~1.6.3", "name": "http-errors", "escapedName": "http-errors", "rawSpec": "~1.6.3", "saveSpec": null, "fetchSpec": "~1.6.3"}, "_requiredBy": ["/", "/body-parser", "/raw-body", "/send"], "_resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "_shasum": "8b55680bb4be283a0b5bf4ea2e38580be1d9320d", "_spec": "http-errors@~1.6.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "deprecated": false, "description": "Create HTTP error objects", "devDependencies": {"eslint": "4.18.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.9.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "homepage": "https://github.com/jshttp/http-errors#readme", "keywords": ["http", "error"], "license": "MIT", "name": "http-errors", "repository": {"type": "git", "url": "git+https://github.com/jshttp/http-errors.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "version": "1.6.3"}