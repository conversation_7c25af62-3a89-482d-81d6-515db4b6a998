{"_from": "uglify-to-browserify@~1.0.0", "_id": "uglify-to-browserify@1.0.2", "_inBundle": false, "_integrity": "sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q==", "_location": "/uglify-to-browserify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uglify-to-browserify@~1.0.0", "name": "uglify-to-browserify", "escapedName": "uglify-to-browserify", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/uglify-js"], "_resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "_shasum": "6e0924d6bda6b5afe349e39a6d632850a0f882b7", "_spec": "uglify-to-browserify@~1.0.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/uglify-js", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/ForbesLindesay/uglify-to-browserify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "A transform to make UglifyJS work in browserify.", "devDependencies": {"source-map": "~0.1.27", "uglify-js": "~2.4.0"}, "homepage": "https://github.com/ForbesLindesay/uglify-to-browserify#readme", "keywords": [], "license": "MIT", "name": "uglify-to-browserify", "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/uglify-to-browserify.git"}, "scripts": {"test": "node test/index.js"}, "version": "1.0.2"}