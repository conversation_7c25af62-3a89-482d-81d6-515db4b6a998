{"_from": "raw-body@2.3.3", "_id": "raw-body@2.3.3", "_inBundle": false, "_integrity": "sha512-9esiElv1BrZoI3rCDuOuKCBRbuApGGaDPQfjSflGxdy4oyzqghxu6klEkkVIvBje+FF0BX9coEv8KqW6X/7njw==", "_location": "/raw-body", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "raw-body@2.3.3", "name": "raw-body", "escapedName": "raw-body", "rawSpec": "2.3.3", "saveSpec": null, "fetchSpec": "2.3.3"}, "_requiredBy": ["/body-parser"], "_resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.3.tgz", "_shasum": "1b324ece6b5706e153855bc1148c65bb7f6ea0c3", "_spec": "raw-body@2.3.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/raw-body/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"bytes": "3.0.0", "http-errors": "1.6.3", "iconv-lite": "0.4.23", "unpipe": "1.0.0"}, "deprecated": false, "description": "Get and validate the raw body of a readable stream.", "devDependencies": {"bluebird": "3.5.1", "eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.11.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.1.0", "istanbul": "0.4.5", "mocha": "2.5.3", "readable-stream": "2.3.6", "safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.d.ts", "index.js"], "homepage": "https://github.com/stream-utils/raw-body#readme", "license": "MIT", "name": "raw-body", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/raw-body.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --trace-deprecation --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --trace-deprecation --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --trace-deprecation --reporter spec --check-leaks test/"}, "version": "2.3.3"}