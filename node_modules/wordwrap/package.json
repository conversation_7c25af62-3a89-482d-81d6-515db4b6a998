{"_from": "wordwrap@~0.0.2", "_id": "wordwrap@0.0.3", "_inBundle": false, "_integrity": "sha512-1tMA907+V4QmxV7dbRvb4/8MaRALK6q9Abid3ndMYnbyo8piisCmeONVqVSXqQA3KaP4SLt5b7ud6E2sqP8TFw==", "_location": "/wordwrap", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wordwrap@~0.0.2", "name": "wordwrap", "escapedName": "wordwrap", "rawSpec": "~0.0.2", "saveSpec": null, "fetchSpec": "~0.0.2"}, "_requiredBy": ["/optimist"], "_resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "_shasum": "a3d5da6cd5c0bc0008d37234bbaf1bed63059107", "_spec": "wordwrap@~0.0.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/optimist", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-wordwrap/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Wrap those words. Show them at what columns to start and stop.", "devDependencies": {"expresso": "=0.7.x"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/substack/node-wordwrap#readme", "keywords": ["word", "wrap", "rule", "format", "column"], "license": "MIT", "main": "./index.js", "name": "wordwrap", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "scripts": {"test": "expresso"}, "version": "0.0.3"}