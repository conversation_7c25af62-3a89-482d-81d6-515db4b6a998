{"_from": "graceful-readlink@>= 1.0.0", "_id": "graceful-readlink@1.0.1", "_inBundle": false, "_integrity": "sha512-8tLu60LgxF6XpdbK8OW3FA+IfTNBn1ZHGHKF4KQbEeSkajYw5PlYJcKluntgegDPTg8UkHjpet1T82vk6TQ68w==", "_location": "/graceful-readlink", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "graceful-readlink@>= 1.0.0", "name": "graceful-readlink", "escapedName": "graceful-readlink", "rawSpec": ">= 1.0.0", "saveSpec": null, "fetchSpec": ">= 1.0.0"}, "_requiredBy": ["/clean-css/commander"], "_resolved": "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz", "_shasum": "4cafad76bc62f02fa039b2f94e9a3dd3a391a725", "_spec": "graceful-readlink@>= 1.0.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/clean-css/node_modules/commander", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/zhiyelee/graceful-readlink/issues"}, "bundleDependencies": false, "deprecated": false, "description": "graceful fs.readlink", "homepage": "https://github.com/zhiyelee/graceful-readlink", "keywords": ["fs.readlink", "readlink"], "license": "MIT", "main": "index.js", "name": "graceful-readlink", "repository": {"type": "git", "url": "git://github.com/zhiyelee/graceful-readlink.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.1"}