{"_from": "jstransformer@0.0.2", "_id": "jstransformer@0.0.2", "_inBundle": false, "_integrity": "sha512-b7tmf91j1ChMuYhwbPBnNgB62dmHuqiHpOdd6QLKzde8HydZqm+ud3qWreGWecSxPBFFNOf1Ozjx0xo2plFdHA==", "_location": "/jstransformer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "jstransformer@0.0.2", "name": "jstransformer", "escapedName": "jstransformer", "rawSpec": "0.0.2", "saveSpec": null, "fetchSpec": "0.0.2"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/jstransformer/-/jstransformer-0.0.2.tgz", "_shasum": "7aae29a903d196cfa0973d885d3e47947ecd76ab", "_spec": "jstransformer@0.0.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/jstransformers/jstransformer/issues"}, "bundleDependencies": false, "dependencies": {"is-promise": "^2.0.0", "promise": "^6.0.1"}, "deprecated": false, "description": "Normalize the API of any jstransformer", "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "testit": "^1.2.0"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/jstransformers/jstransformer#readme", "keywords": ["jstransformer"], "license": "MIT", "name": "jstransformer", "repository": {"type": "git", "url": "git+https://github.com/jstransformers/jstransformer.git"}, "scripts": {"coverage": "istanbul cover test", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls", "test": "node test"}, "version": "0.0.2"}