{"_from": "commander@~2.6.0", "_id": "commander@2.6.0", "_inBundle": false, "_integrity": "sha512-PhbTMT+ilDXZKqH8xbvuUY2ZEQNef0Q7DKxgoEKb4ccytsdvVVJmYqR0sGbi96nxU6oGrwEIQnclpK2NBZuQlg==", "_location": "/commander", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "commander@~2.6.0", "name": "commander", "escapedName": "commander", "rawSpec": "~2.6.0", "saveSpec": null, "fetchSpec": "~2.6.0"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/commander/-/commander-2.6.0.tgz", "_shasum": "9df7e52fb2a0cb0fb89058ee80c3104225f37e1d", "_spec": "commander@~2.6.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "the complete solution for node.js command-line programs", "devDependencies": {"should": ">= 0.0.1"}, "engines": {"node": ">= 0.6.x"}, "files": ["index.js"], "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["command", "option", "parser", "prompt"], "license": "MIT", "main": "index", "name": "commander", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"test": "make test"}, "version": "2.6.0"}