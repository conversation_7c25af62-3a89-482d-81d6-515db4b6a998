{"_from": "longest@^1.0.1", "_id": "longest@1.0.1", "_inBundle": false, "_integrity": "sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg==", "_location": "/longest", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "longest@^1.0.1", "name": "longest", "escapedName": "longest", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/align-text"], "_resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "_shasum": "30a0b2da38f73770e8294a0d22e6625ed77d0097", "_spec": "longest@^1.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/align-text", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/longest/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Get the longest item in an array.", "devDependencies": {"fill-range": "^2.1.0", "mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/longest", "keywords": ["array", "element", "item", "long", "length", "longest"], "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/longest/blob/master/LICENSE"}, "main": "index.js", "name": "longest", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/longest.git"}, "scripts": {"test": "mocha"}, "version": "1.0.1"}