{"_from": "css-parse@1.0.4", "_id": "css-parse@1.0.4", "_inBundle": false, "_integrity": "sha512-pfstzKVRZiHprDXdsmtfH1HYUEw22lzjuHdnpe1hscwoQvgW2C5zDQIBE0RKoALEReTn9W1ECdY8uaT/kO4VfA==", "_location": "/css-parse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "css-parse@1.0.4", "name": "css-parse", "escapedName": "css-parse", "rawSpec": "1.0.4", "saveSpec": null, "fetchSpec": "1.0.4"}, "_requiredBy": ["/css"], "_resolved": "https://registry.npmjs.org/css-parse/-/css-parse-1.0.4.tgz", "_shasum": "38b0503fbf9da9f54e9c1dbda60e145c77117bdd", "_spec": "css-parse@1.0.4", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/css", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bundleDependencies": false, "deprecated": false, "description": "CSS parser", "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["css", "parser", "stylesheet"], "main": "index", "name": "css-parse", "version": "1.0.4"}