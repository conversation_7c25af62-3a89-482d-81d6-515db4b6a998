{"_from": "is-buffer@^1.1.5", "_id": "is-buffer@1.1.6", "_inBundle": false, "_integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "_location": "/is-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-buffer@^1.1.5", "name": "is-buffer", "escapedName": "is-buffer", "rawSpec": "^1.1.5", "saveSpec": null, "fetchSpec": "^1.1.5"}, "_requiredBy": ["/kind-of"], "_resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "_shasum": "efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be", "_spec": "is-buffer@^1.1.5", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/kind-of", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/is-buffer/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Determine if an object is a Buffer", "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/is-buffer#readme", "keywords": ["buffer", "buffers", "type", "core buffer", "browser buffer", "browserify", "typed array", "uint32array", "int16array", "int32array", "float32array", "float64array", "browser", "arraybuffer", "dataview"], "license": "MIT", "main": "index.js", "name": "is-buffer", "repository": {"type": "git", "url": "git://github.com/feross/is-buffer.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "testling": {"files": "test/*.js"}, "version": "1.1.6"}