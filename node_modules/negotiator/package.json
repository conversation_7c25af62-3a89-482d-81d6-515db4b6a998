{"_from": "negotiator@0.6.3", "_id": "negotiator@0.6.3", "_inBundle": false, "_integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "_location": "/negotiator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "negotiator@0.6.3", "name": "negotiator", "escapedName": "negotiator", "rawSpec": "0.6.3", "saveSpec": null, "fetchSpec": "0.6.3"}, "_requiredBy": ["/accepts"], "_resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "_shasum": "58e323a72fedc0d6f9cd4d31fe49f51479590ccd", "_spec": "negotiator@0.6.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/accepts", "bugs": {"url": "https://github.com/jshttp/negotiator/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "deprecated": false, "description": "HTTP content negotiation", "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "HISTORY.md", "LICENSE", "index.js", "README.md"], "homepage": "https://github.com/jshttp/negotiator#readme", "keywords": ["http", "content negotiation", "accept", "accept-language", "accept-encoding", "accept-charset"], "license": "MIT", "name": "negotiator", "repository": {"type": "git", "url": "git+https://github.com/jshttp/negotiator.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "0.6.3"}