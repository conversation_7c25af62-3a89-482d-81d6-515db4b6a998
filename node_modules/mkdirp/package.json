{"_from": "mkdirp@~0.5.0", "_id": "mkdirp@0.5.6", "_inBundle": false, "_integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "_location": "/mkdirp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mkdirp@~0.5.0", "name": "mkdirp", "escapedName": "mkdirp", "rawSpec": "~0.5.0", "saveSpec": null, "fetchSpec": "~0.5.0"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "_shasum": "7def03d2432dcae4ba1d611445c48396062255f6", "_spec": "mkdirp@~0.5.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"mkdirp": "bin/cmd.js"}, "bugs": {"url": "https://github.com/substack/node-mkdirp/issues"}, "bundleDependencies": false, "dependencies": {"minimist": "^1.2.6"}, "deprecated": false, "description": "Recursively mkdir, like `mkdir -p`", "devDependencies": {"tap": "^16.0.1"}, "files": ["bin", "index.js"], "homepage": "https://github.com/substack/node-mkdirp#readme", "keywords": ["mkdir", "directory"], "license": "MIT", "main": "index.js", "name": "mkdirp", "publishConfig": {"tag": "legacy"}, "repository": {"type": "git", "url": "git+https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "version": "0.5.6"}