{"_from": "serve-static@1.13.2", "_id": "serve-static@1.13.2", "_inBundle": false, "_integrity": "sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw==", "_location": "/serve-static", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "serve-static@1.13.2", "name": "serve-static", "escapedName": "serve-static", "rawSpec": "1.13.2", "saveSpec": null, "fetchSpec": "1.13.2"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.2.tgz", "_shasum": "095e8472fd5b46237db50ce486a43f4b86c6cec1", "_spec": "serve-static@1.13.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "bundleDependencies": false, "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.2", "send": "0.16.2"}, "deprecated": false, "description": "Serve static files", "devDependencies": {"eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "2.5.3", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/serve-static#readme", "license": "MIT", "name": "serve-static", "repository": {"type": "git", "url": "git+https://github.com/expressjs/serve-static.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.13.2"}