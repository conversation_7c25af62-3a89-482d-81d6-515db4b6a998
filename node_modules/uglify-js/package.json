{"_from": "uglify-js@^2.4.19", "_id": "uglify-js@2.8.29", "_inBundle": false, "_integrity": "sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w==", "_location": "/uglify-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uglify-js@^2.4.19", "name": "uglify-js", "escapedName": "uglify-js", "rawSpec": "^2.4.19", "saveSpec": null, "fetchSpec": "^2.4.19"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz", "_shasum": "29c5733148057bb4e1f75df35b7a9cb72e6a59dd", "_spec": "uglify-js@^2.4.19", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "browserify": {"transform": ["uglify-to-browserify"]}, "bugs": {"url": "https://github.com/mishoo/UglifyJS2/issues"}, "bundleDependencies": false, "dependencies": {"source-map": "~0.5.1", "uglify-to-browserify": "~1.0.0", "yargs": "~3.10.0"}, "deprecated": false, "description": "JavaScript parser, mangler/compressor and beautifier toolkit", "devDependencies": {"acorn": "~5.0.3", "mocha": "~2.3.4"}, "engines": {"node": ">=0.8.0"}, "files": ["bin", "lib", "tools", "LICENSE"], "homepage": "http://lisperator.net/uglifyjs", "keywords": ["uglify", "uglify-js", "minify", "minifier"], "license": "BSD-2-<PERSON><PERSON>", "main": "tools/node.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}], "name": "uglify-js", "optionalDependencies": {"uglify-to-browserify": "~1.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/mishoo/UglifyJS2.git"}, "scripts": {"test": "node test/run-tests.js"}, "version": "2.8.29"}