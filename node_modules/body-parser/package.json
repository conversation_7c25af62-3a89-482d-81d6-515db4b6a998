{"_from": "body-parser@1.18.3", "_id": "body-parser@1.18.3", "_inBundle": false, "_integrity": "sha512-YQyoqQG3sO8iCmf8+hyVpgHHOv0/hCEFiS4zTGUwTA1HjAFX66wRcNQrVCeJq9pgESMRvUAOvSil5MJlmccuKQ==", "_location": "/body-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "body-parser@1.18.3", "name": "body-parser", "escapedName": "body-parser", "rawSpec": "1.18.3", "saveSpec": null, "fetchSpec": "1.18.3"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.3.tgz", "_shasum": "5b292198ffdd553b3a0f20ded0592b956955c8b4", "_spec": "body-parser@1.18.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"bytes": "3.0.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "~1.6.3", "iconv-lite": "0.4.23", "on-finished": "~2.3.0", "qs": "6.5.2", "raw-body": "2.3.3", "type-is": "~1.6.16"}, "deprecated": false, "description": "Node.js body parsing middleware", "devDependencies": {"eslint": "4.19.1", "eslint-config-standard": "11.0.0", "eslint-plugin-import": "2.11.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-promise": "3.7.0", "eslint-plugin-standard": "3.1.0", "istanbul": "0.4.5", "methods": "1.1.2", "mocha": "2.5.3", "safe-buffer": "5.1.2", "supertest": "1.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/body-parser#readme", "license": "MIT", "name": "body-parser", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "version": "1.18.3"}