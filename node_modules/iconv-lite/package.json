{"_from": "iconv-lite@0.4.23", "_id": "iconv-lite@0.4.23", "_inBundle": false, "_integrity": "sha512-neyTUVFtahjf0mB3dZT77u+8O0QB89jFdnBkd5P1JgYPbPaia3gXXOVL2fq8VyU2gMMD7SaN7QukTB/pmXYvDA==", "_location": "/iconv-lite", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "iconv-lite@0.4.23", "name": "iconv-lite", "escapedName": "iconv-lite", "rawSpec": "0.4.23", "saveSpec": null, "fetchSpec": "0.4.23"}, "_requiredBy": ["/body-parser", "/raw-body"], "_resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.23.tgz", "_shasum": "297871f63be507adcfbfca715d0cd0eed84e9a63", "_spec": "iconv-lite@0.4.23", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/extend-node": false, "./lib/streams": false}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "bundleDependencies": false, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "deprecated": false, "description": "Convert character encodings in pure javascript.", "devDependencies": {"async": "*", "errto": "*", "iconv": "*", "istanbul": "*", "mocha": "^3.1.0", "request": "~2.81.0", "semver": "*", "unorm": "*"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "keywords": ["iconv", "convert", "charset", "icu"], "license": "MIT", "main": "./lib/index.js", "name": "iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "scripts": {"coverage": "istanbul cover _mocha -- --grep .", "coverage-open": "open coverage/lcov-report/index.html", "test": "mocha --reporter spec --grep ."}, "typings": "./lib/index.d.ts", "version": "0.4.23"}