{"_from": "source-map@0.4.x", "_id": "source-map@0.4.4", "_inBundle": false, "_integrity": "sha512-Y8nIfcb1s/7DcobUz1yOO1GSp7gyL+D9zLHDehT7iRESqGSxjJ448Sg7rvfgsRJCnKLdSl11uGf0s9X80cH0/A==", "_location": "/source-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-map@0.4.x", "name": "source-map", "escapedName": "source-map", "rawSpec": "0.4.x", "saveSpec": null, "fetchSpec": "0.4.x"}, "_requiredBy": ["/clean-css"], "_resolved": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "_shasum": "eba4f5da9c0dc999de68032d8b4f76173652036b", "_spec": "source-map@0.4.x", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/clean-css", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"amdefine": ">=0.0.4"}, "deprecated": false, "description": "Generates and consumes source maps", "devDependencies": {"dryice": ">=0.4.8"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=0.8.0"}, "files": ["lib/", "build/"], "homepage": "https://github.com/mozilla/source-map", "license": "BSD-3-<PERSON><PERSON>", "main": "./lib/source-map.js", "name": "source-map", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"build": "node Makefile.dryice.js", "test": "node test/run-tests.js"}, "version": "0.4.4"}