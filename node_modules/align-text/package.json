{"_from": "align-text@^0.1.3", "_id": "align-text@0.1.4", "_inBundle": false, "_integrity": "sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg==", "_location": "/align-text", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "align-text@^0.1.3", "name": "align-text", "escapedName": "align-text", "rawSpec": "^0.1.3", "saveSpec": null, "fetchSpec": "^0.1.3"}, "_requiredBy": ["/center-align", "/right-align"], "_resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "_shasum": "0cd90a561093f35d0a99256c22b7069433fad117", "_spec": "align-text@^0.1.3", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/center-align", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/align-text/issues"}, "bundleDependencies": false, "dependencies": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "deprecated": false, "description": "Align the text in a string.", "devDependencies": {"mocha": "*", "should": "*", "word-wrap": "^1.0.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/align-text", "keywords": ["align", "align-center", "alignment", "center", "center-align", "indent", "pad", "padding", "right", "right-align", "text", "typography"], "license": "MIT", "main": "index.js", "name": "align-text", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/align-text.git"}, "scripts": {"test": "mocha"}, "version": "0.1.4"}