{"_from": "css-stringify@1.0.5", "_id": "css-stringify@1.0.5", "_inBundle": false, "_integrity": "sha512-aIThpcErhG5EyHorGqNlTh0TduNBqLrrXLO3x5rku3ZKBxuVfY+T7noyM2G2X/01iQANqJUb6d3+FLoa+N7Xwg==", "_location": "/css-stringify", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "css-stringify@1.0.5", "name": "css-stringify", "escapedName": "css-stringify", "rawSpec": "1.0.5", "saveSpec": null, "fetchSpec": "1.0.5"}, "_requiredBy": ["/css"], "_resolved": "https://registry.npmjs.org/css-stringify/-/css-stringify-1.0.5.tgz", "_shasum": "b0d042946db2953bb9d292900a6cb5f6d0122031", "_spec": "css-stringify@1.0.5", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/css", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bundleDependencies": false, "deprecated": false, "description": "CSS compiler", "devDependencies": {"css-parse": "1.0.3", "mocha": "*", "should": "*"}, "keywords": ["css", "stringify", "stylesheet"], "main": "index", "name": "css-stringify", "version": "1.0.5"}