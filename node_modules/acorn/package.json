{"_from": "acorn@^2.1.0", "_id": "acorn@2.7.0", "_inBundle": false, "_integrity": "sha512-pXK8ez/pVjqFdAgBkF1YPVRacuLQ9EXBKaKWaeh58WNfMkCmZhOZzu+NtKSPD5PHmCCHheQ5cD29qM1K4QTxIg==", "_location": "/acorn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "acorn@^2.1.0", "name": "acorn", "escapedName": "acorn", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/acorn-globals", "/constantinople"], "_resolved": "https://registry.npmjs.org/acorn/-/acorn-2.7.0.tgz", "_shasum": "ab6e7d9d886aaca8b085bc3312b79a198433f0e7", "_spec": "acorn@^2.1.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/constantinople", "bin": {"acorn": "bin/acorn"}, "bugs": {"url": "https://github.com/ternjs/acorn/issues"}, "bundleDependencies": false, "contributors": [{"name": "List of Acorn contributors. Updated before every release."}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "ForbesLindesay"}, {"name": "Forbes Lindesay"}, {"name": "<PERSON><PERSON>"}, {"name": "impinball"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "krator"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON> 'p01' Henri"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "PlNG"}, {"name": "r-e-d"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "zsjforcn"}], "deprecated": false, "description": "ECMAScript parser", "devDependencies": {"babel-core": "^5.6.15", "babelify": "^6.1.2", "browserify": "^10.2.4", "browserify-derequire": "^0.9.4", "unicode-7.0.0": "~0.1.5"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/ternjs/acorn", "license": "MIT", "main": "dist/acorn.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://rreverser.com/"}], "name": "acorn", "repository": {"type": "git", "url": "git+https://github.com/ternjs/acorn.git"}, "scripts": {"prepublish": "node bin/build-acorn.js", "test": "node test/run.js"}, "version": "2.7.0"}