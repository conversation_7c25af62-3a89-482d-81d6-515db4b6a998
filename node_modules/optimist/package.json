{"_from": "optimist@~0.3.5", "_id": "optimist@0.3.7", "_inBundle": false, "_integrity": "sha512-TCx0dXQzVtSCg2OgY/bO9hjM9cV4XYx09TVK+s3+FhkjT6LovsLe+pPMzpWf+6yXK/hUizs2gUoTw3jHM0VaTQ==", "_location": "/optimist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "optimist@~0.3.5", "name": "optimist", "escapedName": "optimist", "rawSpec": "~0.3.5", "saveSpec": null, "fetchSpec": "~0.3.5"}, "_requiredBy": ["/transformers/uglify-js"], "_resolved": "https://registry.npmjs.org/optimist/-/optimist-0.3.7.tgz", "_shasum": "c90941ad59e4273328923074d2cf2e7cbc6ec0d9", "_spec": "optimist@~0.3.5", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/transformers/node_modules/uglify-js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/node-optimist/issues"}, "bundleDependencies": false, "dependencies": {"wordwrap": "~0.0.2"}, "deprecated": false, "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "engine": {"node": ">=0.4"}, "homepage": "https://github.com/substack/node-optimist#readme", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "license": "MIT/X11", "main": "./index.js", "name": "optimist", "repository": {"type": "git", "url": "git+ssh://**************/substack/node-optimist.git"}, "scripts": {"test": "tap ./test/*.js"}, "version": "0.3.7"}