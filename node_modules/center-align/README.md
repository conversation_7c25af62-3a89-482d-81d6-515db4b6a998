# center-align [![NPM version](https://badge.fury.io/js/center-align.svg)](http://badge.fury.io/js/center-align)

> Center-align the text in a string.

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i center-align --save
```

## Usage

```js
var centerAlign = require('center-align');
```

**Example**

If used on the following:

```
Lorem ipsum dolor sit amet,
consectetur adipiscing
elit, sed do eiusmod tempor incididunt
ut labore et dolore
magna aliqua. Ut enim ad minim
veniam, quis
```

The result would be:

```
     Lorem ipsum dolor sit amet,
        consectetur adipiscing
elit, sed do eiusmod tempor incididunt
         ut labore et dolore
    magna aliqua. Ut enim ad minim
             veniam, quis
```

## Related projects

* [align-text](https://www.npmjs.com/package/align-text): Align the text in a string. | [homepage](https://github.com/jonschlinkert/align-text)
* [justified](https://www.npmjs.com/package/justified): Wrap words to a specified length and justified the text. | [homepage](https://github.com/jonschlinkert/justified)
* [right-align](https://www.npmjs.com/package/right-align): Right-align the text in a string. | [homepage](https://github.com/jonschlinkert/right-align)
* [word-wrap](https://www.npmjs.com/package/word-wrap): Wrap words to a specified length. | [homepage](https://github.com/jonschlinkert/word-wrap)

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/center-align/issues/new).

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on October 27, 2015._