{"_from": "center-align@^0.1.1", "_id": "center-align@0.1.3", "_inBundle": false, "_integrity": "sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ==", "_location": "/center-align", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "center-align@^0.1.1", "name": "center-align", "escapedName": "center-align", "rawSpec": "^0.1.1", "saveSpec": null, "fetchSpec": "^0.1.1"}, "_requiredBy": ["/cliui"], "_resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "_shasum": "aa0d32629b6ee972200411cbd4461c907bc2b7ad", "_spec": "center-align@^0.1.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/cliui", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/center-align/issues"}, "bundleDependencies": false, "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "deprecated": false, "description": "Center-align the text in a string.", "devDependencies": {"mocha": "^2.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "utils.js"], "homepage": "https://github.com/jonschlinkert/center-align", "keywords": ["align", "align-center", "center", "center-align", "right", "right-align", "text", "typography"], "license": "MIT", "main": "index.js", "name": "center-align", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/center-align.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"description": "", "list": ["align-text", "right-align", "justified", "word-wrap"]}}, "version": "0.1.3"}