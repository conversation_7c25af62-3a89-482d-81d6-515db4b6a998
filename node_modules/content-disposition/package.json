{"_from": "content-disposition@0.5.2", "_id": "content-disposition@0.5.2", "_inBundle": false, "_integrity": "sha512-kRGRZw3bLlFISDBgwTSA1TMBFN6J6GWDeubmDE3AF+3+yXL8hTWv8r5rkLbqYXY4RjPk/EzHnClI3zQf1cFmHA==", "_location": "/content-disposition", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "content-disposition@0.5.2", "name": "content-disposition", "escapedName": "content-disposition", "rawSpec": "0.5.2", "saveSpec": null, "fetchSpec": "0.5.2"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.2.tgz", "_shasum": "0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4", "_spec": "content-disposition@0.5.2", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/express", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Create and parse Content-Disposition header", "devDependencies": {"eslint": "3.11.1", "eslint-config-standard": "6.2.1", "eslint-plugin-promise": "3.3.0", "eslint-plugin-standard": "2.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/content-disposition#readme", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "name": "content-disposition", "repository": {"type": "git", "url": "git+https://github.com/jshttp/content-disposition.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "0.5.2"}