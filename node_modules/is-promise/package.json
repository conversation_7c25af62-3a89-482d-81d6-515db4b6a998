{"_from": "is-promise@^2.0.0", "_id": "is-promise@2.2.2", "_inBundle": false, "_integrity": "sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==", "_location": "/is-promise", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-promise@^2.0.0", "name": "is-promise", "escapedName": "is-promise", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/jstransformer"], "_resolved": "https://registry.npmjs.org/is-promise/-/is-promise-2.2.2.tgz", "_shasum": "39ab959ccbf9a774cf079f7b40c7a26f763135f1", "_spec": "is-promise@^2.0.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jstransformer", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/then/is-promise/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Test whether an object looks like a promises-a+ promise", "devDependencies": {"better-assert": "^1.0.2", "mocha": "~1.7.4"}, "files": ["index.js", "index.mjs"], "homepage": "https://github.com/then/is-promise#readme", "license": "MIT", "main": "./index.js", "name": "is-promise", "repository": {"type": "git", "url": "git+https://github.com/then/is-promise.git"}, "scripts": {"test": "mocha -R spec"}, "version": "2.2.2"}