{"_from": "basic-auth@~2.0.0", "_id": "basic-auth@2.0.1", "_inBundle": false, "_integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "_location": "/basic-auth", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "basic-auth@~2.0.0", "name": "basic-auth", "escapedName": "basic-auth", "rawSpec": "~2.0.0", "saveSpec": null, "fetchSpec": "~2.0.0"}, "_requiredBy": ["/morgan"], "_resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "_shasum": "b998279bf47ce38344b4f3cf916d4679bbf51e3a", "_spec": "basic-auth@~2.0.0", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/morgan", "bugs": {"url": "https://github.com/jshttp/basic-auth/issues"}, "bundleDependencies": false, "dependencies": {"safe-buffer": "5.1.2"}, "deprecated": false, "description": "node.js basic auth parser", "devDependencies": {"eslint": "5.6.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "5.2.0"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/basic-auth#readme", "keywords": ["basic", "auth", "authorization", "<PERSON><PERSON><PERSON>"], "license": "MIT", "name": "basic-auth", "repository": {"type": "git", "url": "git+https://github.com/jshttp/basic-auth.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "2.0.1"}