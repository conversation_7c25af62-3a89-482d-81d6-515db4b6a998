{"_from": "character-parser@1.2.1", "_id": "character-parser@1.2.1", "_inBundle": false, "_integrity": "sha512-6OEBVBlf/y8LaAphnbAnt743O3zMhlBer+FO5D40H6wqAdU9B1TvuApkejgLW0cvv0tEZNLktv1AnRI+C87ueQ==", "_location": "/character-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "character-parser@1.2.1", "name": "character-parser", "escapedName": "character-parser", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/character-parser/-/character-parser-1.2.1.tgz", "_shasum": "c0dde4ab182713b919b970959a123ecc1a30fcd6", "_spec": "character-parser@1.2.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/ForbesLindesay/character-parser/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Parse JavaScript one character at a time to look for snippets in Templates.  This is not a validator, it's just designed to allow you to have sections of JavaScript delimited by brackets robustly.", "devDependencies": {"better-assert": "~1.0.0", "mocha": "~1.9.0"}, "homepage": "https://github.com/ForbesLindesay/character-parser#readme", "keywords": ["parser", "JavaScript", "bracket", "nesting", "comment", "string", "escape", "escaping"], "license": "MIT", "main": "index.js", "name": "character-parser", "repository": {"type": "git", "url": "git+https://github.com/ForbesLindesay/character-parser.git"}, "scripts": {"test": "mocha -R spec"}, "version": "1.2.1"}