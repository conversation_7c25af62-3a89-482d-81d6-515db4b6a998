{"_from": "amdefine@>=0.0.4", "_id": "amdefine@1.0.1", "_inBundle": false, "_integrity": "sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==", "_location": "/amdefine", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "amdefine@>=0.0.4", "name": "amdefine", "escapedName": "amdefine", "rawSpec": ">=0.0.4", "saveSpec": null, "fetchSpec": ">=0.0.4"}, "_requiredBy": ["/source-map", "/transformers/source-map"], "_resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "_shasum": "4a5282ac164729e93619bcfd3ad151f817ce91f5", "_spec": "amdefine@>=0.0.4", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/source-map", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/jrburke"}, "bugs": {"url": "https://github.com/jrburke/amdefine/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Provide AMD's define() API for declaring modules in the AMD format", "engines": {"node": ">=0.4.2"}, "homepage": "http://github.com/jrburke/amdefine", "license": "BSD-3-<PERSON><PERSON> OR MIT", "main": "./amdefine.js", "name": "amdefine", "repository": {"type": "git", "url": "git+https://github.com/jrburke/amdefine.git"}, "version": "1.0.1"}