{"_from": "promise@^6.0.1", "_id": "promise@6.1.0", "_inBundle": false, "_integrity": "sha512-O+uwGKreKNKkshzZv2P7N64lk6EP17iXBn0PbUnNQhk+Q0AHLstiTrjkx3v5YBd3cxUe7Sq6KyRhl/A0xUjk7Q==", "_location": "/promise", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "promise@^6.0.1", "name": "promise", "escapedName": "promise", "rawSpec": "^6.0.1", "saveSpec": null, "fetchSpec": "^6.0.1"}, "_requiredBy": ["/jstransformer"], "_resolved": "https://registry.npmjs.org/promise/-/promise-6.1.0.tgz", "_shasum": "2ce729f6b94b45c26891ad0602c5c90e04c6eef6", "_spec": "promise@^6.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jstransformer", "author": {"name": "ForbesLindesay"}, "bugs": {"url": "https://github.com/then/promise/issues"}, "bundleDependencies": false, "dependencies": {"asap": "~1.0.0"}, "deprecated": false, "description": "Bare bones Promises/A+ implementation", "devDependencies": {"better-assert": "*", "mocha": "*", "promises-aplus-tests": "*"}, "homepage": "https://github.com/then/promise#readme", "license": "MIT", "main": "index.js", "name": "promise", "repository": {"type": "git", "url": "git+https://github.com/then/promise.git"}, "scripts": {"test": "mocha --timeout 200 --slow 99999", "test-extensions": "mocha test/extensions-tests.js -R spec --timeout 200 --slow 999999", "test-resolve": "mocha test/resolver-tests.js -R spec --timeout 200 --slow 999999"}, "version": "6.1.0"}