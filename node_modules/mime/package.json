{"_from": "mime@1.4.1", "_id": "mime@1.4.1", "_inBundle": false, "_integrity": "sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ==", "_location": "/mime", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "mime@1.4.1", "name": "mime", "escapedName": "mime", "rawSpec": "1.4.1", "saveSpec": null, "fetchSpec": "1.4.1"}, "_requiredBy": ["/send"], "_resolved": "https://registry.npmjs.org/mime/-/mime-1.4.1.tgz", "_shasum": "121f9ebc49e3766f311a76e1fa1c8003c4b03aa6", "_spec": "mime@1.4.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/send", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa"}, "bin": {"mime": "cli.js"}, "bugs": {"url": "https://github.com/broofa/node-mime/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/bentomas"}], "dependencies": {}, "deprecated": false, "description": "A comprehensive library for mime-type mapping", "devDependencies": {"mime-db": "1.30.0"}, "homepage": "https://github.com/broofa/node-mime#readme", "keywords": ["util", "mime"], "license": "MIT", "main": "mime.js", "name": "mime", "repository": {"url": "git+https://github.com/broofa/node-mime.git", "type": "git"}, "scripts": {"prepublish": "node build/build.js > types.json", "test": "node build/test.js"}, "version": "1.4.1"}