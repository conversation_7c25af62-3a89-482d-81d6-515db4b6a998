{"_from": "void-elements@~2.0.1", "_id": "void-elements@2.0.1", "_inBundle": false, "_integrity": "sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==", "_location": "/void-elements", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "void-elements@~2.0.1", "name": "void-elements", "escapedName": "void-elements", "rawSpec": "~2.0.1", "saveSpec": null, "fetchSpec": "~2.0.1"}, "_requiredBy": ["/jade"], "_resolved": "https://registry.npmjs.org/void-elements/-/void-elements-2.0.1.tgz", "_shasum": "c066afb582bb1cb4128d60ea92392e94d5e9dbec", "_spec": "void-elements@~2.0.1", "_where": "/Users/<USER>/workspace/matwavetoken/metwavetoken-server/node_modules/jade", "author": {"name": "hemanth.hm"}, "bugs": {"url": "https://github.com/hemanth/void-elements/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Array of \"void elements\" defined by the HTML specification.", "devDependencies": {"cheerio": "^0.18.0"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/hemanth/void-elements", "keywords": ["html", "void", "elements"], "license": "MIT", "main": "index.js", "name": "void-elements", "repository": {"type": "git", "url": "git+https://github.com/hemanth/void-elements.git"}, "scripts": {"prepublish": "node pre-publish.js > index.js", "test": "node test"}, "version": "2.0.1"}