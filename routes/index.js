var express = require("express");
var router = express.Router();

// Import the dummy data service (you'll need to provide this)
const dummyDataService = require("../services/dummyDataService");

// Get all tokens
router.get("/tokens", async (req, res) => {
  try {
    const tokens = await dummyDataService.getAllTokens();
    res.json(tokens);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch tokens" });
  }
});

// Get specific token by type
router.get("/tokens/:type", async (req, res) => {
  try {
    const { type } = req.params;
    if (type !== "black" && type !== "green") {
      return res.status(400).json({ error: "Invalid token type" });
    }

    const token = await dummyDataService.getTokenByType(type);
    if (!token) {
      return res.status(404).json({ error: "Token not found" });
    }

    res.json(token);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch token" });
  }
});

// Get wallet balances
router.get("/wallet/:address/balances", async (req, res) => {
  try {
    const { address } = req.params;
    const balances = await dummyDataService.getWalletBalances(address);

    // Get token details for each balance
    const balancesWithTokens = await Promise.all(
      balances.map(async (balance) => {
        const token = await dummyDataService.getToken(balance.tokenId);
        return { ...balance, token };
      })
    );

    res.json(balancesWithTokens);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch wallet balances" });
  }
});

// Update wallet balance (for testing/demo purposes)
router.post("/wallet/:address/balance", async (req, res) => {
  try {
    const { address } = req.params;
    const { tokenId, balance } = req.body;

    if (!tokenId || balance === undefined) {
      return res.status(400).json({ error: "Missing tokenId or balance" });
    }

    const updatedBalance = await dummyDataService.updateWalletBalance(
      address,
      tokenId,
      balance.toString()
    );
    res.json(updatedBalance);
  } catch (error) {
    res.status(500).json({ error: "Failed to update wallet balance" });
  }
});

// Create transaction
router.post("/transactions", async (req, res) => {
  try {
    const transaction = await dummyDataService.createTransaction(req.body);
    res.status(201).json(transaction);
  } catch (error) {
    res.status(500).json({ error: "Failed to create transaction" });
  }
});

// Get transaction history
router.get("/transactions/:address", async (req, res) => {
  try {
    const { address } = req.params;
    const transactions = await dummyDataService.getTransactionHistory(address);
    res.json(transactions);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch transaction history" });
  }
});

// Update transaction status
router.patch("/transactions/:id/status", async (req, res) => {
  try {
    const { id } = req.params;
    const { status, txHash } = req.body;

    if (!status) {
      return res.status(400).json({ error: "Status is required" });
    }

    const transaction = await dummyDataService.updateTransactionStatus(
      parseInt(id),
      status,
      txHash
    );
    if (!transaction) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    res.json(transaction);
  } catch (error) {
    res.status(500).json({ error: "Failed to update transaction status" });
  }
});

// Get analytics for a token
router.get("/analytics/:tokenId", async (req, res) => {
  try {
    const { tokenId } = req.params;
    const { days } = req.query;

    const analytics = await dummyDataService.getAnalytics(
      parseInt(tokenId),
      days ? parseInt(days) : undefined
    );
    res.json(analytics);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch analytics" });
  }
});

// Get total revenue
router.get("/analytics/revenue/total", async (req, res) => {
  try {
    const revenue = await dummyDataService.getTotalRevenue();
    res.json(revenue);
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch total revenue" });
  }
});

// Get total carbon saved
router.get("/analytics/carbon/total", async (req, res) => {
  try {
    const carbonSaved = await dummyDataService.getTotalCarbonSaved();
    res.json({ carbonSaved });
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch carbon savings" });
  }
});

// Token action endpoints (buy, sell, trade, burn)
router.post("/tokens/:tokenId/:action", async (req, res) => {
  try {
    const { tokenId, action } = req.params;
    const { walletAddress, amount } = req.body;
    console.log({ params: { ...req.params }, body: { ...req.body } });

    if (!walletAddress || !amount) {
      return res
        .status(400)
        .json({ error: "Wallet address and amount are required" });
    }

    if (!["buy", "sell", "trade", "burn"].includes(action)) {
      return res.status(400).json({ error: "Invalid action" });
    }

    const result = await dummyDataService.executeTokenAction(
      parseInt(tokenId),
      action,
      walletAddress.account,
      amount.toString()
    );

    res.json({
      transaction: result.transaction,
      updatedBalance: result.updatedBalance,
      message: `${
        action.charAt(0).toUpperCase() + action.slice(1)
      } order completed successfully`,
    });
  } catch (error) {
    res.status(400).json({
      error: error.message || `Failed to process ${req.params.action} order`,
    });
  }
});

module.exports = router;
